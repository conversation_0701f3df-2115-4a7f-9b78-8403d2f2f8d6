

aiohappyeyeballs==2.4.3
aiohttp==3.10.10
aiosignal==1.3.1
alembic==1.13.3
annotated-types==0.7.0
anyio==4.6.2.post1
appdirs==1.4.4
appnope==0.1.4
asgiref==3.8.1
asttokens==2.4.1
async-timeout==4.0.3
asyncio==3.4.3
attrs==24.2.0
auth0-python==4.7.2
backoff==2.2.1
bcrypt==4.2.0
beautifulsoup4==4.12.3
build==1.2.2.post1
CacheControl==0.14.1
cachetools==5.5.0
certifi==2024.8.30
cffi==1.17.1
charset-normalizer==3.4.0
chroma-hnswlib==0.7.3
chromadb==0.4.24
cleo==2.1.0
click==8.1.7
cohere==5.11.2
coloredlogs==15.0.1
comm==0.2.2
contourpy==1.3.1
crashtest==0.4.1
crewai==0.79.4
crewai-tools==0.14.0
cryptography==43.0.3
cycler==0.12.1
dataclasses-json==0.6.7
debugpy==1.8.7
decorator==5.1.1
Deprecated==1.2.14
deprecation==2.1.0
distlib==0.3.9
distro==1.9.0
docker==7.1.0
docstring_parser==0.16
docx2txt==0.8
dulwich==0.21.7
durationpy==0.9
embedchain==0.1.124
exceptiongroup==1.2.2
executing==2.1.0
fastapi==0.115.4
fastavro==1.9.7
fastjsonschema==2.20.0
filelock==3.16.1
flatbuffers==24.3.25
fonttools==4.55.0
frozenlist==1.5.0
fsspec==2024.10.0
google-ai-generativelanguage==0.6.10
google-api-core==2.22.0
google-api-python-client==2.154.0
google-auth==2.35.0
google-auth-httplib2==0.2.0
google-cloud-aiplatform==1.71.0
google-cloud-bigquery==3.26.0
google-cloud-core==2.4.1
google-cloud-resource-manager==1.13.0
google-cloud-storage==2.18.2
google-crc32c==1.6.0
google-generativeai==0.8.3
google-resumable-media==2.7.2
googleapis-common-protos==1.65.0
gptcache==0.1.44
grpc-google-iam-v1==0.13.1
grpcio==1.67.1
grpcio-status==1.62.3
grpcio-tools==1.62.3
h11==0.14.0
h2==4.1.0
hpack==4.0.0
httpcore==1.0.6
httplib2==0.22.0
httptools==0.6.4
httpx==0.27.2
httpx-sse==0.4.0
huggingface-hub==0.26.2
humanfriendly==10.0
hyperframe==6.0.1
idna==3.10
importlib_metadata==8.4.0
importlib_resources==6.4.5
iniconfig==2.0.0
installer==0.7.0
instructor==1.6.3
ipykernel==6.29.5
ipython==8.29.0
jaraco.classes==3.4.0
jedi==0.19.1
Jinja2==3.1.4
jiter==0.5.0
json_repair==0.30.0
jsonpatch==1.33
jsonpickle==3.3.0
jsonpointer==3.0.0
jsonref==1.1.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter_client==8.6.3
jupyter_core==5.7.2
keyring==24.3.1
kiwisolver==1.4.7
kubernetes==31.0.0
lancedb==0.5.7
langchain==0.3.7
langchain-cohere==0.3.1
langchain-community==0.3.5
langchain-core==0.3.15
langchain-experimental==0.3.2
langchain-google-genai==2.0.5
langchain-openai==0.2.5
langchain-text-splitters==0.3.2
langsmith==0.1.138
litellm==1.51.2
Mako==1.3.6
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.23.0
matplotlib==3.9.2
matplotlib-inline==0.1.7
mdurl==0.1.2
mem0ai==0.1.26
mmh3==5.0.1
monotonic==1.6
more-itertools==10.5.0
mpmath==1.3.0
msgpack==1.1.0
multidict==6.1.0
mypy-extensions==1.0.0
nest-asyncio==1.6.0
networkx==3.4.2
nodeenv==1.9.1
numpy==1.26.4
oauthlib==3.2.2
onnxruntime==1.19.2
openai==1.53.0
opentelemetry-api==1.27.0
opentelemetry-exporter-otlp-proto-common==1.27.0
opentelemetry-exporter-otlp-proto-grpc==1.27.0
opentelemetry-exporter-otlp-proto-http==1.27.0
opentelemetry-instrumentation==0.48b0
opentelemetry-instrumentation-asgi==0.48b0
opentelemetry-instrumentation-fastapi==0.48b0
opentelemetry-proto==1.27.0
opentelemetry-sdk==1.27.0
opentelemetry-semantic-conventions==0.48b0
opentelemetry-util-http==0.48b0
orjson==3.10.10
outcome==1.3.0.post0
overrides==7.7.0
packaging==24.1
pandas==2.2.3
parameterized==0.9.0
parso==0.8.4
pexpect==4.9.0
pillow==11.0.0
pkginfo==1.11.2
platformdirs==4.3.6
pluggy==1.5.0
poetry==1.8.4
poetry-core==1.9.1
poetry-plugin-export==1.8.0
portalocker==2.10.1
portkey-ai==1.10.1
posthog==3.7.0
prompt_toolkit==3.0.48
propcache==0.2.0
proto-plus==1.25.0
protobuf==4.25.5
psutil==6.1.0
ptyprocess==0.7.0
pulsar-client==3.5.0
pure_eval==0.2.3
py==1.11.0
pyarrow==18.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
pydantic==2.9.2
pydantic-settings==2.6.0
pydantic_core==2.23.4
Pygments==2.18.0
PyJWT==2.9.0
pylance==0.9.18
pyparsing==3.2.0
pypdf==5.1.0
PyPika==0.48.9
pyproject_hooks==1.2.0
pyright==1.1.387
pysbd==0.3.4
PySocks==1.7.1
pytest==8.3.3
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-multipart==0.0.19
pytube==15.0.0
pytz==2024.2
pyvis==0.3.2
PyYAML==6.0.2
pyzmq==26.2.0
qdrant-client==1.12.1
RapidFuzz==3.10.1
ratelimiter==1.2.0.post0
referencing==0.35.1
regex==2024.9.11
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
retry==0.9.2
rich==13.9.3
rpds-py==0.20.1
rsa==4.9
schema==0.7.7
selenium==4.26.0
semver==3.0.2
shapely==2.0.6
shellingham==1.5.4
six==1.16.0
sniffio==1.3.1
sortedcontainers==2.4.0
soupsieve==2.6
SQLAlchemy==2.0.35
stack-data==0.6.3
starlette==0.41.2
sympy==1.13.3
tabulate==0.9.0
tenacity==9.0.0
tiktoken==0.7.0
tokenizers==0.20.1
tomli==2.0.2
tomli_w==1.1.0
tomlkit==0.13.2
tornado==6.4.1
tqdm==4.66.6
traitlets==5.14.3
trio==0.27.0
trio-websocket==0.11.1
trove-classifiers==2024.10.21.16
typer==0.12.5
types-requests==2.32.0.20241016
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2024.2
uritemplate==4.1.1
urllib3==2.2.3
uv==0.4.29
uvicorn==0.32.0
uvloop==0.21.0
virtualenv==20.27.1
watchfiles==0.24.0
wcwidth==0.2.13
websocket-client==1.8.0
websockets==13.1
wrapt==1.16.0
wsproto==1.2.0
xattr==1.1.0
yarl==1.17.1
zipp==3.20.2
