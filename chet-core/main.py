from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
import uuid 
import os
import json
import uvicorn
import shutil
from pydantic import BaseModel
from src.processing import process_files, process_questions

app = FastAPI()

#Directories
UPLOAD_DIR = "data/uploads"
RESPONSE_DIR = "data/responses"

os.makedirs(UPLOAD_DIR, exist_ok=True)
os.makedirs(RESPONSE_DIR,exist_ok=True)

#In-memory
job_status = {}
question_status = {}

#CORS config
app.add_middleware(
    CORSMiddleware,
    allow_origins = ["*"],
    allow_credentials = True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class DecisionInput(BaseModel):
    decision: str

@app.post("/upload")
async def upload_files(
    background_tasks: BackgroundTasks, 
    resume: UploadFile=File(...),
    job_description: UploadFile=File()
    ):

    job_id = str(uuid.uuid4())
    

    resume_filename = f"{job_id}_resume_{resume.filename}"
    resume_path = os.path.join(UPLOAD_DIR, resume_filename)
    with open(resume_path, "wb") as buffer:
        shutil.copyfileobj(resume.file, buffer)


    jd_filename = f"{job_id}_jd_{job_description.filename}"
    jd_path = os.path.join(UPLOAD_DIR,jd_filename)
    with open(jd_path,"wb") as buffer:
        shutil.copyfileobj(job_description.file,buffer)

    job_status[job_id]= {"critic":"pending", 
                         "questions":"pending",
                         "resume_path":resume_path,
                         "jd_path":jd_path}
    
    background_tasks.add_task(process_files, job_id, resume_path,jd_path, RESPONSE_DIR)

    return {"jobId":job_id}


@app.get("/status/{job_id}/critic")
def get_status(job_id:str):
    status = job_status.get(job_id)
    print("####status: ",status)
    if not status:
        raise HTTPException(status_code = 404, detail= "Job ID not found")
    
    return { "status":status["critic"]}


@app.get("/result/{job_id}/critic")
def get_result(job_id: str):
    status = job_status.get(job_id)
    if not status:
        raise HTTPException(status_code = 404, detail = "Job ID not found")
    if status["critic"]!="completed":
        raise HTTPException(status_code = 400, detail = "Result not ready yet.")
    
    response_filename = f"{job_id}_critic.md"
    response_path = os.path.join(RESPONSE_DIR,response_filename)

    
    if not os.path.exists(response_path):
        raise HTTPException(status_code=404, detail="Response file not found.")
    
    with open(response_path,'r') as f:
        critic_data = json.load(f)

    markdown_content = {
        "jd_resume_critic": critic_data.get("jd_resume_critic"),
        "conclusion": critic_data.get("conclusion"),
        "percentage": critic_data.get("percentage")
    }

    return markdown_content


@app.post("/regenerate/{job_id}/critic")
def regenerate_critic(job_id:str, background_tasks: BackgroundTasks):
    status = job_status.get(job_id)
    if not status:
        raise HTTPException(status_code=404, details="Job ID not Found....")
    
    job_status[job_id]["critic"]="pending"
    resume_path=status["resume_path"]
    jd_path=status["jd_path"]
    background_tasks.add_task(process_files,job_id,resume_path,jd_path,RESPONSE_DIR)



@app.post("/decision/{job_id}")
def handle_decision( job_id:str, decision_input: DecisionInput, background_tasks: BackgroundTasks):
    print("#############2")
    print(job_id)
    status = job_status.get(job_id)

    print("##########",status)

    

    if not status:
        raise HTTPException(status_code =404, detail= "Job ID not found")
    
    if status['critic'] != "completed":
        raise HTTPException(status_code = 404, detail= "Critic Report not completed yet.")
    
    decision = decision_input.decision.lower()

    if decision == 'proceed':
        if status['questions'] == "completed":
            raise HTTPException(status_code = 400, detail = "Questions already generated.")
        
        if status["questions"] == "processing":
            raise HTTPException(status_code=400, detail = "Questions generation already in progress.")
        
        job_status[job_id]["questions"] = "processing"

        background_tasks.add_task(process_questions, job_id, RESPONSE_DIR)

        return {'jobId': job_id, "status":"processing"}
    
    elif decision == 'reject':
        job_status[job_id]["questions"] = "rejected"
        return {"jobId": job_id, "status":"rejected"}
    
    else :
        raise HTTPException(status_code= 400, detail="Invalid decision. Use 'proceed' or 'reject' .")
    


@app.get("/status/{job_id}/questions")
def get_questions_status(job_id: str):
    status = job_status.get(job_id)
    if not status:
        raise HTTPException(status_code= 404, detail="Job ID not found")
    return {"jobId":job_id, "status": status["questions"] }


@app.get("/result/{job_id}/questions")
def get_questions(job_id:str):
    status = job_status.get(job_id)

    if not status:
        raise HTTPException(status_code=404, detail= "Job ID not found")
    if status["questions"]!="completed":
        raise HTTPException(status_code= 404, detail = "Questions not ready yet.")
    
    questions_filename = f"{job_id}_questions.md"
    questions_path = os.path.join(RESPONSE_DIR, questions_filename)
    if not os.path.exists(questions_path):
        raise HTTPException(status_code=404, detail="Questions file not found")
    
    with open(questions_path,'r') as f:
        markdown_content = f.read()

    return {"content": markdown_content}


if __name__ == "__main__":
    port = int(os.environ.get("PORT",8080))
    uvicorn.run("main:app", host="0.0.0.0",port=port)