FROM python:3.10-slim

ENV PYTHONDONTWRITEBTYECODE =1
ENV PYTHONUNBUFFERED=1

WORKDIR /app

RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install --upgrade pip
RUN pip install --no-cache-dir -r requirements.txt

COPY main.py .
COPY src/ /app/src/
COPY crews/  /app/crews/

RUN mkdir -p /app/data/uploads /app/data/responses /app/data/outputs_flows


CMD ["python", "main.py"] 

# "--host", "0.0.0.0""--port", "8000"]