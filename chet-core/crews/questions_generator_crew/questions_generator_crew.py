from crewai import Agent, Crew, Process, Task
from crewai.project import <PERSON><PERSON><PERSON>, agent, crew, task
from crewai_tools import PDFSearchTool
from langchain_openai import ChatOpenAI
from langchain_google_genai import ChatGoogleGenerativeAI
from portkey_ai import createHeaders, PORTKEY_GATEWAY_URL


@CrewBase
class QuestionsGenCrew :
    agents_config = "config/agents.yaml"
    tasks_config = "config/tasks.yaml"
    llm = ChatOpenAI(model_name='gpt-4o-2024-11-20', temperature=0.2,
                     base_url = PORTKEY_GATEWAY_URL, 
                     default_headers = createHeaders(
                         api_key = "01EAkFpVaMLyIZD+F9BE4IO7PqU7",
                         virtual_key = "open-ai-virtual-b04ce5"
                     )) #o1-mini-2024-09-12 #gpt-4o-2024-11-20 #gpt-4-turbo-2024-04-09
    #llm = ChatGoogleGenerativeAI(model="gemini-1.5-pro", temperature=0.2) #gemini-1.5-flash #gemini-1.5-pro-002

    @agent
    def interview_planner_agent(self) -> Agent:
        return Agent(
            config=self.agents_config['interview_planner_agent'],
            llm=self.llm
        )
    
    @agent
    def questions_agent(self) -> Agent:
        return Agent(
            config=self.agents_config['questions_agent'],
            llm=self.llm
        )

    @task 
    def interview_planning_task(self) -> Task:
        return Task(
            config=self.tasks_config['interview_planning_task'],
            agent=self.interview_planner_agent()
        )
    

    @task
    def questions_analysis(self) -> Task:
        return Task(
            config=self.tasks_config['questions_analysis'],
            agent=self.questions_agent(),
            context= [self.interview_planning_task()]
        )

    @crew
    def crew(self) -> Crew:
        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process = Process.sequential,
            verbose=False
        )