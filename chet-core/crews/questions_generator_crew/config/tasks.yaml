interview_planning_task:
  description: >
    Use the below provided parsed job description and candidate's resume, create a comprehensive interview plan that will guide the generation of interview questions.
 
    **Inputs:**
    - **Parsed Job Description**: {interview_context}
    - **Parsed Candidate Resume**: {findings_resume}
    - **Interview Type and Stage (optional)**: {interview_type_stage}
    - **Interview Difficulty (optional)**: {interview_difficulty}
 
    **Instructions:**
 
    1. **Determine Interview Context:**
       - Identify or confirm the interview type and stage.
       - If not provided, infer based on the information available.
 
    2. **Role Overview:**
       - Summarize the role:
         - Role Title
         - Seniority Level
         - Key Responsibilities
         - Required Technical Skills
         - Required Soft Skills
         - Qualifications/Certifications
 
    3. **Candidate Overview:**
       - Summarize the candidate's background:
         - Total Years of Experience
         - Relevant Experience to the Role
         - Key Skills and Competencies
         - Qualifications/Certifications
         - Notable Achievements
 
    4. **Identify Key Focus Areas:**
       - Compare the role requirements with the candidate's background.
       - Identify areas where:
         - The candidate's experience aligns strongly with the role.
         - There are potential gaps or areas needing further exploration.
       - Determine the competencies and topics that should be prioritized during the interview.
 
    5. **Determine Question Categories and Proportions:**
       - Based on the interview type, role, and seniority level, recommend the categories of questions to be asked (e.g., Technical Skills, Leadership, Problem-Solving).
       - Assign proportions or emphasis to each category to guide the question generation (e.g., 50% Technical Skills, 30% Leadership, 20% Cultural Fit).
 
    **Constraints:**
    - Do not include any speculative or irrelevant information.
    - Maintain a professional and objective tone.
    - Ensure the plan is tailored to the specific role and candidate.
 
  expected_output: >
    Fill the below requirements as per the context provided.
    Interview Plan:
      Interview Context:
        Interview Type: <interview_type>
        Interview Stage: <interview_stage>
      Role Overview:
        Role Title: <role_title>
        Seniority Level: <seniority_level>
        Key Responsibilities:
          - <responsibility_1>
          - <responsibility_2>
          - ...
        Required Technical Skills:
          - <technical_skill_1>
          - <technical_skill_2>
          - ...
        Required Soft Skills:
          - <soft_skill_1>
          - <soft_skill_2>
          - ...
        Qualifications/Certifications:
          - <qualification_1>
          - <qualification_2>
          - ...
      Candidate Overview:
        Total Years of Experience: <years_experience>
        Relevant Experience:
          - <relevant_experience_1>
          - <relevant_experience_2>
          - ...
        Key Skills and Competencies:
          - <skill_1>
          - <skill_2>
          - ...
        Qualifications/Certifications:
          - <qualification_1>
          - <qualification_2>
          - ...
        Notable Achievements:
          - <achievement_1>
          - <achievement_2>
          - ...
      Key Focus Areas:
        - <focus_area_1>: <reason_1>
        - <focus_area_2>: <reason_2>
        - ...
      Question Categories and Proportions:
        - <category_1>: <proportion_1>% (e.g., if the interview type and stage is Technical, focus on skill_1: 40%)
        - <category_2>: <proportion_2>%
        - ...
 
    **Note:**
    - If the interview is a technical round for a senior role, Technical Skills would be emphasized more.
    - For a managerial role, Leadership and Strategic Thinking would have higher proportions.





questions_analysis:
  description: >
    Using the provided context of the interview context.
    Generate a set of tailored, realistic, human-like interview questions for the specified role and candidate. 
    Each question should be accompanied by a **Thought Process** that explains 
    why the question is being asked and what the interviewer should look for in the candidate's response.
    
    **Inputs:**
        - **Interview Type and Stage (optional)**: {interview_type_stage}
        - **Interview Difficulty (optional)**: {interview_difficulty}
 
    **Instructions:**
 
    1. **Understand the Interview Context:**
       - Review the interview type and stage from the Interview Plan to tailor the tone and depth of the questions appropriately.
 
    2. **Analyze the Role and Candidate Overviews:**
       - Deeply understand the role's responsibilities, required skills, and qualifications.
       - Consider the candidate's experience, skills, and notable achievements.
 
    3. **Focus on Key Areas:**
       - Generate questions that directly address the **Key Focus Areas** identified in the Interview Plan.
 
    4. **Adhere to Question Categories and Proportions:**
       - Ensure the questions cover the specified categories and follow the recommended proportions.
       - Need to be specific questions to be framed on the job use related skill's keyword and built few followups as well.
 
    5. **Craft Realistic, Human-Like Questions:**
       - Use natural, professional language as an experienced interviewer would.
       - Ensure questions are open-ended to encourage detailed responses.
 

    Based on the skills, interview difficulty level, and parsed job description (JD), generate a mix of closed-ended and open-ended interview questions. The questions should assess both factual knowledge (through closed-ended questions) and the candidate’s deeper understanding and practical application (through open-ended questions), also that focus on 4 to 5 key concepts of each key skills relevant to the role. The questions should assess both theoretical understanding and hands-on problem-solving abilities.


    Instructions:

    Closed-ended questions should be used to verify the candidate's basic skills and experience with specific technologies, frameworks, or tools mentioned in the JD.
    Open-ended questions should be used to probe deeper into the candidate’s problem-solving skills, thought processes, and their ability to apply their knowledge to real-world scenarios
    
    For example 
    If the skills are Java and the difficulty level is medium, generate questions that cover key concepts such as:
    Design Patterns: Explore the candidate’s understanding and experience with commonly used design patterns (e.g., Singleton, Factory, Observer).
    Memory Management and Garbage Collection: Assess the candidate’s knowledge of Java’s memory management system and how garbage collection works.
    Java Streams: Test the candidate’s ability to use Java Streams for data processing, including filtering, mapping, and reducing.
    Multithreading and Concurrency: Ask questions related to creating threads, synchronization, and handling concurrent tasks in Java.
    Data Structures and Collections: Test the candidate’s understanding of core data structures and collections in Java (e.g., List, Set, Map) and when to use them for optimal performance. If the skills is java, and Difficulty is medium, ask questions on concepts of Design Patterns, Memory Management and Garbage Collection, Java Streams, Multithreading and Concurrency, Data Structures and Collections
        
    **Constraints:**
    - Use only the information provided in the **Interview Plan**.
    - Maintain professionalism and objectivity throughout.
    - Ensure questions are appropriate for the role, seniority level, and interview type.
    - Avoid any discriminatory or inappropriate content.
    - Do not include personal opinions or speculative content.

 
  expected_output: >
    Provide a list of interview questions with accompanying **Thought Processes**, presented in a clear and organized format.
 
    **Output Format Example:**
 
    Interview Questions:
      - Question: "<Question 1>"
        Thought Process: "<Explanation for Question 1>"
      - Question: "<Question 2>"
        Thought Process: "<Explanation for Question 2>"
      - Question: "<Question 3>"
        Thought Process: "<Explanation for Question 3>"
      ...

    **Notes:**
    - Ensure that the number of questions and their focus align with the **Question Categories and Proportions** specified in the Interview Plan.
    - The **Thought Processes** should be concise yet informative, providing clear guidance to the interviewer.