from crewai import Agent, Crew, Process, Task
from crewai.project import Crew<PERSON><PERSON>, agent, crew, task
from crewai_tools import PDFSearchTool
from langchain_openai import ChatOpenAI


@CrewBase
class CandidateResumeCrew :
    agents_config = "config/agents.yaml"
    tasks_config = "config/tasks.yaml"
    llm = ChatOpenAI(model_name='gpt-3.5-turbo', temperature=0)

    @agent
    def desc_res_agent(self) -> Agent:
        return Agent(
            config=self.agents_config['desc_res_agent'],
            llm=self.llm  
        )
 
    @task
    def desc_res_analysis(self) -> Task:
        return Task(
            config=self.tasks_config['desc_res_analysis'],
            agent=self.desc_res_agent()
        )

    @crew
    def crew(self) -> Crew:
        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            verbose=True
        ) 
