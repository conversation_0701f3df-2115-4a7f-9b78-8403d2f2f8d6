 
desc_res_analysis:
  description: >
    Evaluate the findings extracted from a candidate's resume:
    {findings_resume}
    against the specific job description:
    {description}
    for a technical role. Assess whether the candidate's stated skills, experience, and qualifications align with the technical requirements of the position. Identify any gaps, overstatements, or misalignments in their profile, and provide insights into the candidate's suitability for the role.

  expected_output: >
    A comprehensive alignment report that includes:

    - **Skills Match** (Comparison of listed skills against job requirements)
    - **Experience Alignment** (Relevance and adequacy of past job roles, responsibilities, and achievements to the position)
    - **Qualification Fit** (Verification of academic and professional qualifications in relation to the job)
    - **Technical Competency** (Assessment of technical expertise as per the job description)
    - **Areas of Concern** (Noting any discrepancies, gaps, or overstatements in the candidate's profile)
    
    Provide a clear summary of the candidate's overall fit for the role, highlighting strengths, potential concerns, and any recommendations for further assessment or consideration.
