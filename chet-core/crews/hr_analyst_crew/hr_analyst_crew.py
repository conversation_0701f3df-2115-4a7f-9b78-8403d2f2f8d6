from crewai import Agent, Crew, Process, Task
from crewai.project import <PERSON><PERSON><PERSON>, agent, crew, task
from crewai_tools import PDFSearchTool
from langchain_openai import ChatOpenAI
from langchain_google_genai import ChatGoogleGenerativeAI
from pydantic import BaseModel, Field
from portkey_ai import createHeaders, PORTKEY_GATEWAY_URL

class Decision(BaseModel) :
    jd_resume_Critic: str  = Field(..., description = "The detailed analysis of the job description compared to the resume")
    conclusion: str = Field(..., description = "The final decision based on the JD-resume analysis, either 'Yes' or 'No' .")
    percentage: float = Field(..., description = "The total calculated score in percentage. ")

@CrewBase
class HrAnalystCrew() :
    agents_config = "config/agents.yaml"
    tasks_config = "config/tasks.yaml"
    llm = ChatOpenAI(model_name='gpt-4o-2024-11-20', temperature=0.2,
                     base_url = PORTKEY_GATEWAY_URL, 
                     default_headers = createHeaders(
                         api_key = "01EAkFpVaMLyIZD+F9BE4IO7PqU7",
                         virtual_key = "open-ai-virtual-b04ce5"
                     )) 
    #llm = ChatGoogleGenerativeAI(model="gemini-1.5-pro", temperature=0.2)


    @agent
    def pdf_reader_agent(self) -> Agent:
        return Agent(
            config=self.agents_config['pdf_reader_agent'],
            llm=self.llm,
            
        )
    
    @agent
    def desc_res_agent(self) -> Agent:
        return Agent(
            config=self.agents_config['desc_res_agent'],
            llm=self.llm  
        )
    

    @agent
    def job_desc_agent(self) -> Agent:
        return Agent(
            config = self.agents_config['job_desc_agent'],
            llm = self.llm
        )

    # @agent
    # def consistency_visualizer_agent(self) -> Agent: 
    #     return Agent(
    #         config=self.agents_config['consistency_visualizer_agent'],
    #         llm=self.llm
    #     )



    # Creating Tasks
    @task
    def resume_analysis(self) -> Task:
        return Task(
            config=self.tasks_config['resume_analysis'],
            agent=self.pdf_reader_agent(),
            tools=[PDFSearchTool()],
            async_execution=True
        )
    
    # @task
    # def linkedin_profile_analysis(self) -> Task:
    #     return Task(
    #         config=self.tasks_config['linkedin_profile_analysis'],
    #         agent=self.pdf_reader_agent(),
    #         tools=[PDFSearchTool()],
    #         async_execution=True
    #     )
    

    @task
    def job_desc_analysis(self) -> Task:
        return Task(
            config=self.tasks_config['job_desc_analysis'],
            agent=self.job_desc_agent(),
            tools=[PDFSearchTool()],
            async_execution=True
        )
    

    @task
    def desc_res_analysis(self) -> Task:
        return Task(
            config=self.tasks_config['desc_res_analysis'],
            agent=self.desc_res_agent(),
            context = [self.resume_analysis()],
            output_pydantic=Decision
        )
    
    # @task
    # def discrepancy_visual_analysis(self) ->Task:
    #     return Task(
    #         config=self.tasks_config['discrepancy_visual_analysis'],
    #         agent=self.consistency_visualizer_agent(),
    #         context=[self.resume_analysis(),self.linkedin_profile_analysis()]
    #     )
    

    #Creating Crew
    @crew
    def crew(self)-> Crew: 
        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process = Process.sequential,
            verbose=False
        )

