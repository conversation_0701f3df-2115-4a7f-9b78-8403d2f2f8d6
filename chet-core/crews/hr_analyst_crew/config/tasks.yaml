resume_analysis:
  description: >
    Analyze a candidate's resume {resume_path} in PDF format to extract and categorize key information for recruitment purposes.
  expected_output: >
    Summarized information from the candidate's resume, organized into categories:
    - Personal Information: Full Name, Contact Details (if available)
    - Professional Experience: Job Titles, Companies, Employment Duration, Key Responsibilities
    - Education: Degrees, Institutions, Graduation Dates
    - Skills: Technical Skills, Soft Skills
    - Certifications: Relevant Certifications, Issuing Organizations, Dates
    - Additional Details: Projects, Professional Affiliations, Volunteer Experience
    Format this information consistently for easy reference by the recruitment team.


linkedin_profile_analysis:
  description: >
    Review a candidate's {linkedIn_path} profile PDF to extract and categorize pertinent information.
  expected_output: >
    A structured summary of the candidate's LinkedIn profile, organized as:
    - Personal Information: Full Name, Contact Details (if available)
    - Professional Experience: Job Titles, Companies, Employment Duration, Key Responsibilities
    - Education: Degrees, Institutions, Graduation Dates
    - Skills: Technical Skills, Soft Skills
    - Certifications: Relevant Certifications, Issuing Organizations, Dates
    - Additional Details: Projects, Professional Affiliations, Volunteer Experience
    Ensure data is formatted for easy reference and consistent use by the recruitment team.
 

job_desc_analysis:
  description: >
    Analyze the provided job description in pdf format to extract key role-specific information.
 
    **Inputs:**
    - **Job Description:** {description} 
 
    **Instructions:**
    - **Identify Role Type:**
      - Determine the general category of the role (e.g., Software Engineer, Product Manager, Data Scientist).
    - **Extract Key Responsibilities:**
      - List the primary responsibilities and duties associated with the role.
    - **Identify Required/Preferred Skills and Competencies:**
      - Extract both technical and soft skills required for the position.
    - **Determine Seniority Level:**
      - Infer the seniority level required (e.g., Junior, Mid-Level, Senior, Lead) based on language cues.
    - **Extract Keywords and Phrases:**
      - Identify important keywords and phrases that are indicative of the role's requirements.
    - **Categorize Information:**
      - Organize the extracted information into predefined categories for easy consumption by other agents.
 

  expected_output: >
      - A structured summary in a clear format that includes:
      - **Role Type**
      - **Seniority Level**
      - **Key Responsibilities**
      - **Required/Preferred Technical Skills**
      - **Required Soft Skills**
      - **Keywords and Phrases**
      Ensure that the output is clear, concise, and accurately reflects the information in the job description.




desc_res_analysis:
  description: >
    Conduct a focused comparison of the information extracted from a candidate's resume against the specific job description. 
    Determine whether the candidate meets the necessary criteria for skills, experience, and qualifications.

     Evaluate a candidate's resume from the context against a specified job description in pdf format : 
     {description}. 

    Ensure compliance with standard equal employment opportunity guidelines. 
    Do not discriminate based on age, gender, race, ethnicity, religion, national origin, disability, or any other protected characteristic. 
    Focus solely on the candidate's ability to perform the job duties as described.
    Ensure that your analysis is thorough and avoids unwarranted optimism. 
    Base your conclusions strictly on the provided information.

     The analysis involves assessing the alignment of skills, experience, qualifications, and technical competencies with the requirements of the job.
     Will they able to learn the necessary skills for the job based on their previous skills. 
     Calculate the weighted scores for each criterion and decide if the candidate meets the threshold for suitability.


     Given the resume details and job description provided, be a strict evaluator for the sake of competitiveness of the business.

        1. Assign points to the following criteria based on the resume's alignment with the job requirements:

          - Skills Match(Check for mentioned programming languages, technologies & tools): 30% weight

            - High (3 points): Candidate possesses all required and additional desired skills.

            - Medium (2 points): Candidate meets all required skills.

            - Low (1 point): Candidate is missing few(1-3) key skills.

            - Zero (0 point): Candidate is missing many(more than 3) key skills

          - Experience Alignment(Check for employment durations, keep note of non-internship or academic experience to include or not , position attained): 40% weight

            - High (3 points): Experience exceeds job requirements. Demonstrates a wide range of roles and responsibilities within the field.

            - Medium (2 points): Experience meets the job requirements perfectly.  Meets the required number of years in relevant roles and job functions.

            - Low (1 point): Experience is relevant but does not fully meet the requirements. Duration of employement is less than years of experience required.

            - Zero (0 point): Experience is irrelevant or less relevant. Not in same domain

          - Qualification Fit: 10% weight

            - High (3 points): Qualifications exceed the job's educational or professional requirements.

            - Medium (2 points): Qualifications meet the job's requirements.

            - Low (1 point): Few(1-2) Qualifications fall short of the job's requirements.

            - Zero (0 point): Many(more than 2) Qualifications fall short of the job's requirements.

          - Technical Competency(Check work and projects): 20% weight

            - High (3 points): Demonstrates superior technical expertise necessary for the job.

            - Medium (2 points): Has adequate technical skills required for the job.

            - Low (1 point): Lacks few(1-3) critical technical skills necessary for the job.

            - Zero (0 point): Lacks many(more than 3) critical technical skills necessary for the job.

        2. Calculate the total weighted score using the formula and Total score is 3:

          Calculated total score = (Skills Match Score * 0.30) + (Experience Alignment Score * 0.30) + (Qualification Fit Score * 0.20) + (Technical Competency Score * 0.20)

        3. Determine if the candidate is suitable based on a threshold of 60% out the total score 3.

          - If the Total Score >= 60%, the decision should be 'Yes'.

          - If the Total Score < 60%, the decision should be 'No'.

        Provide the scores for each category and the final decision in separate lines.

  expected_output: >
    Generate a structured Markdown (.md) file should be the output with appropriate Headings and Body
    Skills Match Score: [3, 2, 1, or 0]
    Experience Alignment Score: [3, 2, 1, or 0]
    Qualification Fit Score: [3, 2, 1, or 0]
    Technical Competency Score: [3, 2, 1, or 0]
    Total Weighted Score: [Calculated Value]
    Percentage: [percentage of the total calculated score]
    
    Conclusion : ['Yes' or 'No']

    Instructions:
    Output the calculated scores for each category along with the reason why that score and the final decision based on the total weighted score. 
    Show the calculation steps.
    Along with score also mention why you are giving that point in single line, the reason for the score with highlights and short comings.
    

 

discrepancy_visual_analysis:
  description: >

    Conduct a detailed comparison of the candidate's resume and LinkedIn profile findings to identify discrepancies 
    across key areas such as personal details, work experience, durations, gaps, and education.
  
    Use visual indicators in the report a tick (✅) for matching details and a cross (❌) for discrepancies. 
    Point out the discrepancies side by the cross visualizer
    This analysis should help verify the accuracy and legitimacy of the candidate's information. 
    Retrospective your potential output to nullify the mistakes.
 
  expected_output: >
    A structured Markdown (.md) file that includes:
    - **Personal Information** 
      - ✅ or ❌ (Inconsistencies in name, contact details)
    - **Professional Experience**
      - ✅ or ❌ (Mismatches in job titles, companies, employment duration)
    - **Education**
      - ✅ or ❌ (Discrepancies in degrees, institutions, graduation dates)
    - **Certifications**
      - ✅ or ❌ (Differences in certifications, issuing organizations, dates)
 
    The report should provide a clear summary of findings, highlighting any significant discrepancies and offering insights about the accuracy and legitimacy.
    


