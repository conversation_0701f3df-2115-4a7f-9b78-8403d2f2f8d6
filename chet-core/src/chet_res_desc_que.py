
import os
from pydantic import BaseModel
from crewai.flow.flow import Flow, listen, start, and_

from dotenv import load_dotenv
load_dotenv()
os.environ['OPENAI_API_KEY'] = "***********************************************************************************************"
os.environ['GOOGLE_API_KEY'] = "AIzaSyAtuZERfE2ITaQquMIYxVDFb-ZxPL32SQ0"



from crews.hr_analyst_crew.hr_analyst_crew import HrAnalystCrew, Decision
from crews.questions_generator_crew.questions_generator_crew import QuestionsGenCrew



# #Output files directory
# output_dir = 'data/outputs_flows'
# os.makedirs(output_dir, exist_ok=True)


# def save_output(file_name, content):
#   #path for saving the file 
#   file_path = os.path.join(output_dir,file_name)
  
#   with open(file_path,'w') as file:
#     file.write(content+"\n\n")


#To pass data between different crews
class ManageState(BaseModel):
  resume_path : str = ""
  jd_path : str = ""
  findings_resume : str = ""
  findings_linkedin : str = ""
  jd_resume : Decision = ""
  interview_context : str = ""
  interview_type_stage: str = "Not Available"
  interview_difficulty: str = "Medium"
  questions: str = ""




class ChetFlow(Flow[ManageState]):


    #Kickoff called.....
    @start()
    def get_discrepancies(self):
        print("###############################2")
        crew = HrAnalystCrew().crew()
        output = crew.kickoff(        
           inputs={
          'description': self.state.jd_path,
          'resume_path': self.state.resume_path,
          'linkedIn_path':  "Not Available"  ,     #"./resource/Profile.pdf"
        })
        tasks = crew.tasks
        tasks_dict = {task.name : task.output for task in tasks}
        self.state.findings_resume = tasks_dict['resume_analysis'].raw
        #self.state.findings_linkedin = tasks_dict['linkedin_profile_analysis'].raw
        self.state.jd_resume = tasks_dict['desc_res_analysis'].pydantic
        self.state.interview_context = tasks_dict['job_desc_analysis'].raw
 
        # file_1 = "Discrepancies.md"
        # file_2= "CandidateTechnical.md"
        # save_output(file_1, output.raw)
        # save_output(file_2, str(self.state.jd_resume) )
        # save_output("Description-Report.md", tasks_dict['job_desc_analysis'].raw)
        
    # @listen(get_discrepancies)
    # def decision_review(self):
    #     print("Starting>>>>>>>>>>>>>>>>>\n")
    #     if self.state.jd_resume.conclusion=="No":
    #         user_approval = input("Forward to next step or Reject (Yes/Reject) ")
            
    #         if user_approval.lower() == "yes":
    #             self.state.jd_resume.conclusion="Yes"
                
    #         elif user_approval.lower() == "reject":
    #             self.state.jd_resume.conclusion="No"
    #     print("Ending>>>>>>>>>>>>>>>>>\n")

    
class ChetFlowQues(Flow[ManageState]):



    @start()
    def gen_questions(self):
        crew = QuestionsGenCrew().crew()
        output = crew.kickoff(
            inputs={
            'findings_resume': self.state.findings_resume ,
            'interview_context': self.state.interview_context,
            'interview_type_stage': "Not Available",
            'interview_difficulty': self.state.interview_difficulty
        })  
        
        tasks = crew.tasks
        tasks_dict = {task.name : task.output for task in tasks}
        file_name = "Questions.md"
        self.state.questions = output.raw
        # save_output(file_name, output.raw)
        # save_output("Planner.md", tasks_dict['interview_planning_task'].raw)





