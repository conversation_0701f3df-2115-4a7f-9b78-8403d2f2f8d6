import time
import os
import json
from src.chet_res_desc_que import Chet<PERSON>low, ChetFlowQues, Decision, ManageState

#from src.practice import Chet<PERSON>low, ManageState

_managed_state = {}

def process_files(job_id:str, resume_path:str, jd_path:str, response_dir:str):

    try:
        print(f"##processing job {job_id}...")

        chf= ChetFlow()
        # chf.set_resume_path(resume_path)
        # chf.set_jd_path(jd_path)
        chf.state.resume_path = resume_path
        chf.state.jd_path = jd_path

        chf.kickoff()

        #decision = chf.get_jd_resume()
        decision = chf.state.jd_resume


        response_content = {
            "jd_resume_critic": decision.jd_resume_Critic,
            "conclusion": decision.conclusion,
            "percentage": decision.percentage
        }
        
        response_filename = f"{job_id}_critic.md"
        response_path= os.path.join(response_dir,response_filename)

        with open(response_path, "w") as f:
            json.dump(response_content,f)

        print(f"{job_id} Completed#########")

        _managed_state[job_id] = {
            'findings_resume': chf.state.findings_resume ,
            'interview_context': chf.state.interview_context,
            'interview_type_stage': "Not Available",
            'interview_difficulty': chf.state.interview_difficulty
        }

        from main import job_status
        job_status[job_id]["critic"]="completed"

    except Exception as e:
        print(f"Error processing job {job_id}:  {e}")
        from main import job_status
        job_status[job_id]["critic"]="failed"




def process_questions(job_id: str, response_dir: str):
    try:
        print(f"Generating Questions for Job ID: {job_id}...")


        ques_managed_state = _managed_state.get(job_id)
        if not ques_managed_state:
            raise ValueError(f" No state for question generation")

        chetques = ChetFlowQues()

        chetques.state.findings_resume = ques_managed_state.get('findings_resume')
        chetques.state.interview_context = ques_managed_state.get('interview_context')
        chetques.state.interview_type_stage = ques_managed_state.get('interview_type_stage')
        chetques.state.interview_difficulty = ques_managed_state.get('interview_difficulty')

        chetques.kickoff()

        result = chetques.state.questions

        response_content = result        
        response_filename = f"{job_id}_questions.md"
        response_path= os.path.join(response_dir,response_filename)

        with open(response_path, "w") as f:
            f.write(response_content)

        print(f"{job_id} Completed#########1")

        from main import job_status
        job_status[job_id]["questions"]="completed"

    except Exception as e:
        print(f"Error generating questions {job_id}:  {e}")
        from main import job_status
        job_status[job_id]["questions"]="failed"

    