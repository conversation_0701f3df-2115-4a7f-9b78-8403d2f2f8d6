import { ThemeProvider, Container } from "@mui/material";
import theme from "./theme/theme";
import FileUploader from "./pages/fileUploadpage/FileUploader";
import CriticPage from "./pages/criticpage/CriticPage";
import QuestionsPage from "./pages/questionspage/QuestionsPage";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";

function App() {
  return (
    <ThemeProvider theme={theme}>
      <Router>
        <Container>
          <Routes>
            <Route path="/" element={<FileUploader />} />
            <Route path="/critic/:jobId" element={<CriticPage />} />
            <Route path="/genQuestions/:jobId" element={<QuestionsPage />} />
          </Routes>
        </Container>
      </Router>
    </ThemeProvider>
  );
}

export default App;
