import React, { createContext, useState } from "react";

export const StreamingContext = createContext();

export const StreamingProvider = ({ children }) => {
  const [criticData, setCriticData] = useState({
    streamedContent: "",
    streamingComplete: false,
    conclusion: "",
    percentage: "",
  });

  const [questionsData, setQuestionsData] = useState({
    streamedContent: "",
    streamingComplete: false,
  });

  return (
    <StreamingContext.Provider
      value={{
        criticData,
        setCriticData,
        questionsData,
        setQuestionsData,
      }}
    >
      {children}
    </StreamingContext.Provider>
  );
};
