import React, { useEffect, useState, useRef, useContext } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Box, Button, Typography, CircularProgress } from "@mui/material";
import ReactMarkdown from "react-markdown";
import axios from "axios";
import { StreamingContext } from "../../components/Context/StreamingContext";

function CriticPage() {
  const { jobId } = useParams();
  const navigate = useNavigate();

  //const [jdResumeCritic, setJdResumeCritic] = useState("");
  const { criticData, setCriticData } = useContext(StreamingContext);

  // const [conclusion, setConclusion] = useState("");
  // const [percentage, setPercentage] = useState("");

  //const [streamedCriticContent, setStreamedCriticContent] = useState("");
  const streamingTimeoutRef = useRef(null);
  //const [streamingComplete, setStreamingComplete] = useState(false);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [processingDecision, setProcessingDecision] = useState(false);
  const [regenCount, setRegenCount] = useState(0);

  const RESPONSE_ENDPOINT = process.env.REACT_APP_BACKEND_URL;

  const streamCriticContent = (fullContent) => {
    if (streamingTimeoutRef.current) {
      clearInterval(streamingTimeoutRef.current);
    }

    let currentIndex = 0;
    const intervalTime = 10;

    streamingTimeoutRef.current = setInterval(() => {
      if (currentIndex < fullContent.length) {
        const char = fullContent[currentIndex];
        if (char != undefined) {
          setCriticData((prev) => ({
            ...prev,
            streamedContent: prev.streamedContent + char,
          }));
        }
        currentIndex += 1;
      } else {
        clearInterval(streamingTimeoutRef.current);
        setCriticData((prev) => ({
          ...prev,
          streamingComplete: true,
        }));
        setLoading(false);
      }
    }, intervalTime);
  };

  const fetchMarkdown = async () => {
    try {
      const response = await axios.get(`${RESPONSE_ENDPOINT}/result/${jobId}/critic`);

      const { jd_resume_critic, conclusion, percentage } = response.data;

      //setJdResumeCritic(jd_resume_critic);
      // setConclusion(conclusion);
      // setPercentage(percentage);
      setLoading(false);

      //setStreamedCriticContent("");
      //setStreamingComplete(false);
      setCriticData({
        streamedContent: "",
        streamingComplete: false,
        conclusion: conclusion,
        percentage: percentage,
      });

      streamCriticContent(jd_resume_critic);
    } catch (err) {
      console.error("Error Fetching Markdown", err);
      setError("Failed to fetch the response. Please try again later.");
      setLoading(false);
    }
  };

  useEffect(() => {
    const maxTimeout = 5 * 60 * 1000; // 5 mins in msec

    if (criticData.streamedContent && criticData.streamingComplete) {
      setLoading(false);
      return;
    }
    const checkJobStatus = async () => {
      try {
        const statusResponse = await axios.get(`${RESPONSE_ENDPOINT}/status/${jobId}/critic`);
        const { status } = statusResponse.data;

        if (status === "completed") {
          fetchMarkdown();
          clearInterval(interval);
          clearTimeout(timeout);
        }

        if (status === "failed") {
          setError("Report generation failed...");
          setLoading(false);
          clearInterval(interval);
          clearTimeout(timeout);

          if (streamingTimeoutRef.current) {
            clearInterval(streamingTimeoutRef.current);
          }
        }
      } catch (err) {
        console.error("Error checking job status: ", err);
        console.log("JobID::::::::::::", jobId);
        setError("Failed to check job status. Please try again.");
        setLoading(false);
        clearInterval(interval);
        clearTimeout(timeout);
      }
    };

    checkJobStatus();

    const interval = setInterval(() => {
      checkJobStatus();
    }, 5000);

    const timeout = setTimeout(() => {
      clearInterval(interval);
      setError("Processing is taking longer than expected. Please retry...");
      setLoading(false);
    }, maxTimeout);

    return () => {
      clearInterval(interval);
      clearTimeout(timeout);

      if (streamingTimeoutRef.current) {
        clearInterval(streamingTimeoutRef.current);
      }
      //setStreamingComplete(false);
    };
  }, [jobId, regenCount]);

  const handleProceed = async () => {
    setProcessingDecision(true);
    setError("");

    try {
      const response = await axios.post(`${RESPONSE_ENDPOINT}/decision/${jobId}`, {
        decision: "proceed",
      });

      const { status } = response.data;

      if (status === "processing") {
        navigate(`/genQuestions/${jobId}`);
      }
    } catch (err) {
      console.error("Error handling decision:", err);
      setError("Failed to process your decision.");
      setProcessingDecision(false);
    }
  };

  const handleReject = () => {
    navigate("/");
  };

  const handleRegenerate = async () => {
    try {
      setError("");
      setLoading(true);
      await axios.post(`${RESPONSE_ENDPOINT}/regenerate/${jobId}/critic`);
      setCriticData({
        streamedContent: "",
        streamingComplete: false,
        conclusion: "",
        percentage: "",
      });
      setRegenCount((count) => count + 1);
    } catch (error) {
      console.error("Error re-generate critic:", error);
      setError("Failed to regenerate critic. Please try again.");
      setLoading(false);
    }
  };

  return (
    <Box sx={{ width: "100%", mx: "auto", p: 4 }}>
      <Typography variant="h4" align="center" gutterBottom>
        Job Description versus Resume critic report
      </Typography>

      {/*Loading Indicator*/}
      {loading && !error && (
        <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", mt: 4 }}>
          <CircularProgress />
          <Typography variant="body1" sx={{ ml: 2 }}>
            Processing your request. Please wait...
          </Typography>
        </Box>
      )}

      {/*Error Message*/}
      {error && (
        <Typography variant="body1" color="error" align="center">
          {error}
        </Typography>
      )}

      {/*Display Markdown Content*/}
      {!loading && !error && (
        <Box
          sx={{
            mt: 4,
            mb: 4,
            p: 2,
            border: "1px solid #ccc",
            borderRadius: "8px",
            position: "relative",
          }}
        >
          <Box
            sx={{
              position: "absolute",
              top: 1,
              right: 4,
              backgroundColor: "rgba(255,255,255, 0.7)",
              p: 1,
            }}
          >
            <Typography variant="caption" display="block">
              0 = zero
            </Typography>
            <Typography variant="caption" display="block">
              1 = low
            </Typography>
            <Typography variant="caption" display="block">
              2 = medium
            </Typography>
            <Typography variant="caption" display="block">
              3 = high
            </Typography>
          </Box>

          <Typography variant="h6" gutterBottom>
            JD Resume Critic:
          </Typography>
          <ReactMarkdown>{criticData.streamedContent}</ReactMarkdown>
          {criticData.streamingComplete && (
            <>
              <Typography variant="h6" gutterBottom>
                Conclusion:
              </Typography>
              <Typography variant="body1" gutterBottom>
                {criticData.conclusion}
              </Typography>

              <Typography variant="h6" gutterBottom>
                Percentage:
              </Typography>
              <Typography variant="body1" gutterBottom>
                {criticData.percentage}
              </Typography>
            </>
          )}
        </Box>
      )}

      {/*Proceed and Reject Buttons*/}
      {!loading && !error && (
        <Box sx={{ display: "flex", justifyContent: "center", gap: 2 }}>
          <Button
            variant="contained"
            color="success"
            onClick={handleProceed}
            disabled={processingDecision}
          >
            Proceed
          </Button>
          <Button variant="outlined" color="error" onClick={handleReject}>
            Reject
          </Button>

          <Button variant="contained" color="primary" onClick={handleRegenerate}>
            Regenerate
          </Button>
        </Box>
      )}
    </Box>
  );
}

export default CriticPage;
