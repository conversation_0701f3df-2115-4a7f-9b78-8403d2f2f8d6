import React, { useEffect, useState, useRef, useContext } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Box, Button, Typography, CircularProgress } from "@mui/material";
import ReactMarkdown from "react-markdown";
import axios from "axios";
import { StreamingContext } from "../../components/Context/StreamingContext";

function QuestionsPage() {
  const { jobId } = useParams();
  const naviagte = useNavigate();

  //const [markdownContent, setMarkdownContent] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  //const [streamedQuestions, setStreamedQuestions] = useState("");
  const { questionsData, setQuestionsData } = useContext(StreamingContext);

  const streamingTimeoutRef = useRef(null);

  const RESPONSE_ENDPOINT = process.env.REACT_APP_BACKEND_URL;

  const streamQuestions = (fullContent) => {
    if (streamingTimeoutRef.current) {
      clearInterval(streamingTimeoutRef.current);
    }
    let currentIndex = 0;
    const intervalTime = 10;

    streamingTimeoutRef.current = setInterval(() => {
      if (currentIndex < fullContent.length) {
        //setStreamedQuestions((prev) => prev + fullContent[currentIndex]);
        const char = fullContent[currentIndex];
        if (char !== undefined) {
          setQuestionsData((prev) => ({
            ...prev,
            streamedContent: prev.streamedContent + char,
          }));
        }
        currentIndex += 1;
      } else {
        clearInterval(streamingTimeoutRef.current);
        setQuestionsData((prev) => ({
          ...prev,
          streamingComplete: true,
        }));
        setLoading(false);
      }
    }, intervalTime);
  };
  const fetchQuestions = async () => {
    try {
      const response = await axios.get(`${RESPONSE_ENDPOINT}/result/${jobId}/questions`);

      const { content } = response.data;
      //console.log("######1 :", content);

      //setMarkdownContent(content);
      setLoading(false);
      setQuestionsData({
        streamedContent: "",
        streamingComplete: false,
      });

      //setStreamedQuestions("");

      streamQuestions(content);
    } catch (err) {
      console.log("Erro Fetching Questions", err);
      setError("Failed to fetch the questions. Please try again.");
      setLoading(false);
    }
  };

  useEffect(() => {
    const maxTimeout = 5 * 60 * 1000; // 5mins in msec

    if (questionsData.streamedContent && questionsData.streamingComplete) {
      setLoading(false);
      return;
    }

    const checkQuestionsStatus = async () => {
      try {
        const statusResponse = await axios.get(`${RESPONSE_ENDPOINT}/status/${jobId}/questions`);
        const { status } = statusResponse.data;

        if (status === "completed") {
          fetchQuestions();
          clearInterval(interval);
          clearTimeout(timeout);
        } else if (status === "failed") {
          throw new Error("Questions generation failed.");
        }
      } catch (err) {
        console.error("Error checking question status: ", err);
        setError("Failed to check question status. Please try again");
        setLoading(false);
        clearInterval(interval);
        clearTimeout(timeout);
      }
    };

    checkQuestionsStatus();

    const interval = setInterval(() => {
      checkQuestionsStatus();
    }, 5000);

    const timeout = setTimeout(() => {
      clearInterval(interval);
      setError("Questions generation is taking longer than expected. Please retry.");
      setLoading(false);
    }, maxTimeout);

    return () => {
      clearInterval(interval);
      clearTimeout(timeout);
      if (streamingTimeoutRef.current) {
        clearInterval(streamingTimeoutRef.current);
      }
    };
  }, [jobId]);

  const handleClose = () => {
    naviagte("/");
  };

  return (
    <Box sx={{ width: "100%", mx: "auto", p: 4 }}>
      <Typography variant="h4" align="center" gutterBottom>
        Generated Questions
      </Typography>

      {/*Loading Indicator*/}
      {loading && !error && (
        <Box sx={{ display: "flex", justifyContent: "center", alignItem: "center", mt: 4 }}>
          <CircularProgress />
          <Typography variant="body1" sx={{ ml: 2 }}>
            Generating questions. Please wait...
          </Typography>
        </Box>
      )}

      {/* Error Message*/}
      {error && (
        <Typography variant="body1" color="error" align="center">
          {error}
        </Typography>
      )}

      {/*Display Markdown Content */}
      {!loading && !error && (
        <Box sx={{ mt: 4, mb: 4, justifyContent: "center" }}>
          <ReactMarkdown>{questionsData.streamedContent}</ReactMarkdown>
        </Box>
      )}

      {/*Close Button */}
      {!loading && !error && (
        <Box sx={{ display: "flex", justifyContent: "center", gap: 2 }}>
          <Button variant="contained" color="primary" onClick={handleClose}>
            Close
          </Button>
        </Box>
      )}
    </Box>
  );
}

export default QuestionsPage;
