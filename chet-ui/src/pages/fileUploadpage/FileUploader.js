import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Box, Typography, Button } from "@mui/material";
import axios from "axios";
import FileTypeSelect from "./FileTypeSelect";
import FileDropzone from "./FileDropZone";
import UploadProgress from "./UploadProgress";

function FileUploader() {
  const navigate = useNavigate();

  const [fileType, setFileType] = useState("");

  const [uploadedFiles, setUploadedFiles] = useState({
    resume: { file: null, progress: 0 },
    jobDescription: { file: null, progress: 0 },
  });

  const [submissionMessage, setSubmissionMessage] = useState({
    message: "",
    type: "",
  });

  const BACKEND_ENDPOINT = process.env.REACT_APP_BACKEND_URL;

  const handleFileTypeChange = (event) => {
    setFileType(event.target.value);
  };

  const handleFileUpload = (file) => {
    const type = fileType;
    setUploadedFiles((prev) => ({
      ...prev,
      [type]: { file, progress: 0 },
    }));

    setSubmissionMessage({
      message: "",
      type: "",
    });
  };

  useEffect(() => {
    Object.keys(uploadedFiles).forEach((type) => {
      const fileData = uploadedFiles[type];
      if (fileData.file && fileData.progress < 100) {
        const timer = setInterval(() => {
          setUploadedFiles((prev) => {
            const newProgress = prev[type].progress + 10;
            if (newProgress >= 100) {
              clearInterval(timer);
              return {
                ...prev,
                [type]: { ...prev[type], progress: 100 },
              };
            }
            return {
              ...prev,
              [type]: { ...prev[type], progress: newProgress },
            };
          });
        }, 500);
        return () => clearInterval(timer);
      }
    });
  }, [uploadedFiles]);

  const handleRemoveFile = (type) => {
    setUploadedFiles((prev) => ({
      ...prev,
      [type]: { file: null, progress: 0 },
    }));

    setSubmissionMessage({
      message: "",
      type: "",
    });
  };

  const areFilesReady = () => {
    return (
      uploadedFiles.resume.file &&
      uploadedFiles.jobDescription.file &&
      uploadedFiles.resume.progress >= 100 &&
      uploadedFiles.jobDescription.progress >= 100
    );
  };

  const handleSubmit = async () => {
    if (!areFilesReady()) {
      setSubmissionMessage({
        message: "Please upload both Resume and Job Description files before submitting.",
        type: "error",
      });
      return;
    }

    const formData = new FormData();
    formData.append("resume", uploadedFiles.resume.file);
    formData.append("job_description", uploadedFiles.jobDescription.file);

    try {
      setSubmissionMessage({
        message: "",
        type: "",
      });

      //Make Post request to the backend
      const response = await axios.post(`${BACKEND_ENDPOINT}/upload`, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        onUploadProgress: (progressEvent) => {
          const { loaded, total } = progressEvent;
          const percentage = Math.floor((loaded * 100) / total);
        },
      });

      console.log("Response:   ", response);

      //   await new Promise((resolve) => setTimeout(resolve, 1000));

      //   const response = { status: 200, jobId: 12323443 };

      if (response.status === 200) {
        const { jobId } = response.data;

        setSubmissionMessage({
          message: "Files submitted successfully!",
          type: "success",
        });

        setUploadedFiles({
          resume: { file: null, progress: 0 },
          jobDescription: { file: null, progress: 0 },
        });

        setFileType("");

        navigate(`/critic/${jobId}`);
      } else {
        setSubmissionMessage({
          message: "Unexpected server response. Please try again.",
          type: "error",
        });
      }
    } catch (error) {
      console.log("Error submitting files: ", error);
      console.log("Path:::::::::", `${BACKEND_ENDPOINT}/upload`);
      setSubmissionMessage({
        message: "Failed to submit files. Please try again.",
        type: "error",
      });
    }

    // setSubmissionMessage({
    //   message: "Files submiited successfully!",
    //   type: "success",
    // });

    // setUploadedFiles({
    //   resume: { file: null, progres: 0 },
    //   jobDescription: { file: null, progress: 0 },
    // });

    // setFileType("");
  };

  const handleFailedSubmit = () => {
    setSubmissionMessage({
      message: "There was an error submitting your files.",
      type: "error",
    });
  };

  const isSubmittedDisabled = () => {
    return !areFilesReady();
  };

  return (
    <Box
      sx={{
        maxWidth: 600,
        mx: "auto",
        p: 4,
      }}
    >
      <FileTypeSelect fileType={fileType} onChange={handleFileTypeChange} />

      <FileDropzone fileType={fileType} onFileUpload={handleFileUpload} />

      {Object.keys(uploadedFiles).map((type) => {
        const fileData = uploadedFiles[type];
        return (
          fileData.file && (
            <UploadProgress
              key={type}
              fileType={type}
              file={fileData.file}
              progress={fileData.progress}
              onRemove={() => handleRemoveFile(type)}
            />
          )
        );
      })}

      <Box sx={{ textAlign: "center", mt: 4 }}>
        <Button
          variant="contained"
          color="primary"
          onClick={handleSubmit}
          disabled={isSubmittedDisabled()}
        >
          Submit
        </Button>
      </Box>

      {submissionMessage.message && (
        <Box sx={{ mt: 2, textAlign: "cneter" }}>
          <Typography
            variant="body1"
            color={submissionMessage.type === "success" ? "green" : "error"}
          >
            {submissionMessage.message}
          </Typography>
        </Box>
      )}
    </Box>
  );
}

export default FileUploader;
