import React from "react";
import { Box, Typography, LinearProgress, IconButton } from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";

function UploadProgress({ fileType, file, progress, onRemove }) {
  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        mt: 2,
        p: 2,
        border: "1px solid",
        borderColor: "grey.300",
        borderRadius: 2,
        backgroundColor: "grey.50",
      }}
    >
      <Box sx={{ flexGrow: 1 }}>
        <Typography variant="subtitle1">
          {fileType === "resume" ? "Resume" : "Job Description"}: {file.name}
        </Typography>
        <LinearProgress variant="determinate" value={progress} sx={{ mt: 1 }} />
      </Box>
      <IconButton onClick={onRemove} sx={{ ml: 2 }}>
        <DeleteIcon />
      </IconButton>
    </Box>
  );
}

export default UploadProgress;
