import React, { useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { Paper, Typography } from "@mui/material";

function FileDropzone({ fileType, onFileUpload }) {
  const getAccept = () => {
    switch (fileType) {
      case "resume":
        return {
          "application/pdf": [".pdf"],
          "application/msword": [".doc", ".docx"],
          "text/plain": [".txt"],
        };
      case "description":
        return {
          "application/pdf": [".pdf"],
          "application/msword": [".doc", ".docx"],
          "text/plain": [".txt"],
        };
      default:
        return {};
    }
  };

  const onDrop = useCallback(
    (acceptedFiles) => {
      if (acceptedFiles.length === 0) return;
      onFileUpload(acceptedFiles[0]);
    },
    [onFileUpload]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: false,
    accept: getAccept(),
    disabled: !fileType,
  });

  return (
    <Paper
      {...getRootProps()}
      sx={{
        p: 4,
        height: "250px",
        textAlign: "center",
        color: "text.secondary",
        border: "2px dashed",
        borderColor: isDragActive ? "primary.main" : "grey.400",
        backgroundColor: isDragActive ? "grey.100" : "transparent",
        cursor: fileType ? "pointer" : "not-allowed",
        transition: "border 0.24s ease-in-out",
        mt: 2,
      }}
    >
      <input {...getInputProps()} />
      <Typography variant="h6">
        {isDragActive
          ? "Drop the file here...."
          : fileType
          ? "Drag & drop a file here, or click to select"
          : "Please select a file type to upload"}
      </Typography>
    </Paper>
  );
}

export default FileDropzone;
