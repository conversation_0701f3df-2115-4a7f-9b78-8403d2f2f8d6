import React from "react";
import { FormControl, InputLabel, Select, MenuItem } from "@mui/material";

function FileTypeSelect({ fileType, onChange }) {
  return (
    <FormControl
      sx={{ width: "50%", margin: "0 auto", display: "flex", justifyContent: "center" }}
      margin="normal"
    >
      <InputLabel id="file-type-select-label"> Select File Type</InputLabel>
      <Select
        labelId="file-type-select-label"
        value={fileType}
        label="Select File Type"
        onChange={onChange}
      >
        <MenuItem value="resume">Resume</MenuItem>
        <MenuItem value="jobDescription">Job Description</MenuItem>
      </Select>
    </FormControl>
  );
}

export default FileTypeSelect;
