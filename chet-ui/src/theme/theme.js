import { createTheme, responsiveFontSizes } from "@mui/material";
import { indigo, teal, grey } from "@mui/material/colors";

let theme = createTheme({
  palette: {
    primary: {
      main: indigo[700],
    },
    secondary: {
      main: teal[500],
    },
    background: {
      default: grey[50],
    },
    text: {
      primary: grey[900],
    },
  },
});

theme = responsiveFontSizes(theme);

export default theme;
