FROM node:18-alpine AS build 
WORKDIR /app
COPY package*.json ./

RUN npm cache clean --force
RUN npm install --legacy-peer-deps

COPY . .
RUN npm run build

# CMD npm start


FROM nginx:stable-alpine
COPY --from=build /app/build /usr/share/nginx/html
COPY ./nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 8080
CMD ["nginx","-g", "daemon off;"]


# FROM node:18-alpine AS serve
# WORKDIR /app
# COPY --from=build /app/build ./build
# RUN npm install -g serve
# EXPOSE 8080
# CMD ["serve", "-s", "build", "-l","tcp://0.0.0.0:${PORT:-8080}"]

# FROM nginx:stable-alpine
# RUN rm -rf /usr/share/nginx/html/*
# COPY --from=build /app/build /usr/share/nginx/html

# CMD ["nginx", "-g", "daemon off;"]

