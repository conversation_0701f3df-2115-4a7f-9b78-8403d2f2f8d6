{"name": "chet_ui", "version": "0.1.0", "private": true, "dependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.2.0", "@mui/material": "^6.2.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.9", "react": "^18.0.0", "react-dom": "^18.0.0", "react-dropzone": "^14.3.5", "react-markdown": "^9.0.1", "react-router-dom": "^7.0.2", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-transform-private-property-in-object": "^7.25.9", "ajv": "^8.17.1"}}