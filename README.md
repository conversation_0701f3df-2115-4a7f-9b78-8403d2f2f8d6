# agentic-ai-chet

# chet-core : Backend(Crewai + FastAPI)

- main.py
- src
- crews
- data
- Dockerfile
- Requirements.txt

# chet-ui : Frontend(React)

- src
- Dockerfile
- package.json
- .env

---

## How to run chet-core component/service

### Create a virtual environment: In your project’s parent directory

#### Command for virtual environment creation

$python -m venv </path/to/new/virtual/environment>

#### Command for Activation

$source </path/to/new/virtual/environment>/bin/activate

#### Command for Deactivation

$deactivate

#### Installation of Libraries/Dependencies in virtual environment

$pip install -r requirements.txt

#### Run the application

$uvicorn main:app --reload --host 0.0.0.0 --port 8000

---

## How to run chet-ui component/service

#### Install Libraries/Dependencies: node_modules

$npm install

#### Run the application

$npm start

#### file .env

Contains the backend url, change it as per the usage, localhost/URL.

---

### Create/Logon to GCP account:

https://console.cloud.google.com/

### Enable Cloud Run, Artifact Registry services

### Get privilege/access to project required

Project: CHET-CREWAI

#### Accessing GCP from your local

$gcloud auth login

---

## For Frontend: chet-ui

#### Create Docker Image for chet-ui

$docker build --platform linux/amd64 -t crewai-frontend:latest . #For MAC M1/2 specify platform

#### Tagging the image for pushing w.r.t path

$docker tag crewai-frontend us-east1-docker.pkg.dev/chet-crewai/chet-repo/crewai-frontend:latest

#### Pushing to Artifact Registery

$docker push us-east1-docker.pkg.dev/chet-crewai/chet-repo/crewai-frontend:latest

#### Deploying on Cloud Run from Artifact Registry

$gcloud run deploy crewai-frontend \
--image us-east1-docker.pkg.dev/chet-crewai/chet-repo/crewai-frontend:latest \
--platform managed \
--region us-east1 \
—allow-unauthenticated

---

## For Backend: chet-core

#### Create Docker Image for chet-core

$docker build --platform linux/amd64 -t crewai-backend:latest . #For MAC M1/2 specify platform

#### Tagging the image for pushing w.r.t path

$docker tag crewai-frontend us-east1-docker.pkg.dev/chet-crewai/chet-repo/crewai-backend:latest

#### Pushing to Artifact Registery

$docker push us-east1-docker.pkg.dev/chet-crewai/chet-repo/crewai-backend:latest

#### Deploying on Cloud Run from Artifact Registry

$gcloud run deploy crewai-backend \
--image us-east1-docker.pkg.dev/chet-crewai/chet-repo/crewai-backend:latest \
--platform managed \
--region us-east1 \
--allow-unauthenticated
